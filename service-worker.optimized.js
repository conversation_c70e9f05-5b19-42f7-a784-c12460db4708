/**
 * 优化版 Service Worker
 * 基于 Web Pay Hub 项目的性能分析报告
 * 
 * 主要功能：
 * 1. 静态资源缓存
 * 2. API请求缓存
 * 3. 图片资源缓存
 * 4. 离线支持
 * 5. 缓存更新策略
 */

const CACHE_VERSION = 'v1.0.0'
const STATIC_CACHE = `pay-hub-static-${CACHE_VERSION}`
const DYNAMIC_CACHE = `pay-hub-dynamic-${CACHE_VERSION}`
const IMAGE_CACHE = `pay-hub-images-${CACHE_VERSION}`
const API_CACHE = `pay-hub-api-${CACHE_VERSION}`

// 需要预缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/static/css/chunk-common.css',
  '/static/js/chunk-vendors.js',
  '/static/js/chunk-common.js',
  '/static/js/runtime.js',
  '/favicon.ico'
]

// 缓存策略配置
const CACHE_STRATEGIES = {
  // 静态资源：缓存优先
  static: {
    cacheName: STATIC_CACHE,
    strategy: 'cacheFirst',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    maxEntries: 100
  },
  
  // 图片资源：缓存优先
  images: {
    cacheName: IMAGE_CACHE,
    strategy: 'cacheFirst',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    maxEntries: 200
  },
  
  // API请求：网络优先，失败时使用缓存
  api: {
    cacheName: API_CACHE,
    strategy: 'networkFirst',
    maxAge: 5 * 60 * 1000, // 5分钟
    maxEntries: 50
  },
  
  // 动态内容：网络优先
  dynamic: {
    cacheName: DYNAMIC_CACHE,
    strategy: 'networkFirst',
    maxAge: 24 * 60 * 60 * 1000, // 1天
    maxEntries: 100
  }
}

// Service Worker 安装事件
self.addEventListener('install', event => {
  console.log('[SW] Installing Service Worker...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('[SW] Precaching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('[SW] Static assets cached successfully')
        // 强制激活新的 Service Worker
        return self.skipWaiting()
      })
      .catch(error => {
        console.error('[SW] Failed to cache static assets:', error)
      })
  )
})

// Service Worker 激活事件
self.addEventListener('activate', event => {
  console.log('[SW] Activating Service Worker...')
  
  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      cleanupOldCaches(),
      // 立即控制所有客户端
      self.clients.claim()
    ])
  )
})

// 网络请求拦截
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)
  
  // 只处理同源请求
  if (url.origin !== location.origin) {
    return
  }
  
  // 根据请求类型选择缓存策略
  if (isStaticAsset(url.pathname)) {
    event.respondWith(handleStaticAsset(request))
  } else if (isImageAsset(url.pathname)) {
    event.respondWith(handleImageAsset(request))
  } else if (isApiRequest(url.pathname)) {
    event.respondWith(handleApiRequest(request))
  } else {
    event.respondWith(handleDynamicRequest(request))
  }
})

// 处理静态资源请求
async function handleStaticAsset(request) {
  const config = CACHE_STRATEGIES.static
  
  try {
    // 缓存优先策略
    const cachedResponse = await caches.match(request)
    if (cachedResponse && !isExpired(cachedResponse, config.maxAge)) {
      return cachedResponse
    }
    
    // 缓存未命中或已过期，从网络获取
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      const cache = await caches.open(config.cacheName)
      await cache.put(request, networkResponse.clone())
      await limitCacheSize(config.cacheName, config.maxEntries)
    }
    
    return networkResponse
  } catch (error) {
    console.error('[SW] Static asset fetch failed:', error)
    // 网络失败时返回缓存
    return caches.match(request) || new Response('Network Error', { status: 503 })
  }
}

// 处理图片资源请求
async function handleImageAsset(request) {
  const config = CACHE_STRATEGIES.images
  
  try {
    // 缓存优先策略
    const cachedResponse = await caches.match(request)
    if (cachedResponse && !isExpired(cachedResponse, config.maxAge)) {
      return cachedResponse
    }
    
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      const cache = await caches.open(config.cacheName)
      await cache.put(request, networkResponse.clone())
      await limitCacheSize(config.cacheName, config.maxEntries)
    }
    
    return networkResponse
  } catch (error) {
    console.error('[SW] Image fetch failed:', error)
    return caches.match(request) || createFallbackImage()
  }
}

// 处理API请求
async function handleApiRequest(request) {
  const config = CACHE_STRATEGIES.api
  
  try {
    // 网络优先策略
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      // 只缓存GET请求
      if (request.method === 'GET') {
        const cache = await caches.open(config.cacheName)
        await cache.put(request, networkResponse.clone())
        await limitCacheSize(config.cacheName, config.maxEntries)
      }
    }
    
    return networkResponse
  } catch (error) {
    console.error('[SW] API request failed:', error)
    
    // 网络失败时返回缓存（仅GET请求）
    if (request.method === 'GET') {
      const cachedResponse = await caches.match(request)
      if (cachedResponse && !isExpired(cachedResponse, config.maxAge)) {
        return cachedResponse
      }
    }
    
    throw error
  }
}

// 处理动态请求
async function handleDynamicRequest(request) {
  const config = CACHE_STRATEGIES.dynamic
  
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok && request.method === 'GET') {
      const cache = await caches.open(config.cacheName)
      await cache.put(request, networkResponse.clone())
      await limitCacheSize(config.cacheName, config.maxEntries)
    }
    
    return networkResponse
  } catch (error) {
    console.error('[SW] Dynamic request failed:', error)
    
    // 返回缓存或离线页面
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 返回离线页面
    if (request.destination === 'document') {
      return caches.match('/offline.html') || new Response('Offline', { status: 503 })
    }
    
    throw error
  }
}

// 工具函数：判断是否为静态资源
function isStaticAsset(pathname) {
  return /\.(js|css|woff2?|ttf|eot)$/.test(pathname) || 
         pathname.startsWith('/static/js/') || 
         pathname.startsWith('/static/css/')
}

// 工具函数：判断是否为图片资源
function isImageAsset(pathname) {
  return /\.(png|jpe?g|gif|svg|webp|ico)$/.test(pathname) ||
         pathname.startsWith('/static/img/')
}

// 工具函数：判断是否为API请求
function isApiRequest(pathname) {
  return pathname.startsWith('/api/') || 
         pathname.includes('/ame/') ||
         pathname.includes('/pay/')
}

// 工具函数：检查缓存是否过期
function isExpired(response, maxAge) {
  const dateHeader = response.headers.get('date')
  if (!dateHeader) return false
  
  const responseTime = new Date(dateHeader).getTime()
  return Date.now() - responseTime > maxAge
}

// 工具函数：限制缓存大小
async function limitCacheSize(cacheName, maxEntries) {
  const cache = await caches.open(cacheName)
  const keys = await cache.keys()
  
  if (keys.length > maxEntries) {
    // 删除最旧的条目
    const entriesToDelete = keys.slice(0, keys.length - maxEntries)
    await Promise.all(entriesToDelete.map(key => cache.delete(key)))
  }
}

// 工具函数：清理旧缓存
async function cleanupOldCaches() {
  const cacheNames = await caches.keys()
  const currentCaches = Object.values(CACHE_STRATEGIES).map(config => config.cacheName)
  
  const oldCaches = cacheNames.filter(name => 
    name.startsWith('pay-hub-') && !currentCaches.includes(name)
  )
  
  await Promise.all(oldCaches.map(name => caches.delete(name)))
  console.log('[SW] Cleaned up old caches:', oldCaches)
}

// 工具函数：创建图片加载失败的占位符
function createFallbackImage() {
  // 创建一个简单的SVG占位符
  const svg = `
    <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f0f0f0"/>
      <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999">
        图片加载失败
      </text>
    </svg>
  `
  
  return new Response(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'no-cache'
    }
  })
}

// 监听消息事件（用于缓存管理）
self.addEventListener('message', event => {
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'CLEAR_CACHE':
      clearSpecificCache(payload.cacheName)
      break
      
    case 'GET_CACHE_INFO':
      getCacheInfo().then(info => {
        event.ports[0].postMessage(info)
      })
      break
  }
})

// 清理特定缓存
async function clearSpecificCache(cacheName) {
  if (cacheName) {
    await caches.delete(cacheName)
    console.log(`[SW] Cleared cache: ${cacheName}`)
  }
}

// 获取缓存信息
async function getCacheInfo() {
  const cacheNames = await caches.keys()
  const info = {}
  
  for (const name of cacheNames) {
    const cache = await caches.open(name)
    const keys = await cache.keys()
    info[name] = keys.length
  }
  
  return info
}

console.log('[SW] Service Worker script loaded')
