/**
 * 性能监控工具
 * 基于 Web Pay Hub 项目的性能分析报告
 * 
 * 主要功能：
 * 1. Core Web Vitals 监控
 * 2. 自定义性能指标
 * 3. 资源加载监控
 * 4. 用户交互监控
 * 5. 错误监控
 */

class PerformanceMonitor {
  constructor(options = {}) {
    this.options = {
      // 是否启用监控
      enabled: true,
      // 上报接口
      reportUrl: '/api/performance',
      // 采样率 (0-1)
      sampleRate: 0.1,
      // 批量上报大小
      batchSize: 10,
      // 上报延迟 (ms)
      reportDelay: 5000,
      ...options
    }
    
    this.metrics = []
    this.observers = []
    this.startTime = performance.now()
    
    if (this.options.enabled && this.shouldSample()) {
      this.init()
    }
  }
  
  // 初始化监控
  init() {
    this.observeWebVitals()
    this.observeResourceTiming()
    this.observeUserTiming()
    this.observeLongTasks()
    this.observeLayoutShift()
    this.setupErrorHandling()
    this.setupUnloadHandler()
    
    console.log('[Performance] Monitoring initialized')
  }
  
  // 监控 Core Web Vitals
  observeWebVitals() {
    // First Contentful Paint (FCP)
    this.observePerformanceEntry('paint', (entries) => {
      entries.forEach(entry => {
        if (entry.name === 'first-contentful-paint') {
          this.recordMetric('FCP', entry.startTime, {
            type: 'web-vital',
            unit: 'ms'
          })
        }
      })
    })
    
    // Largest Contentful Paint (LCP)
    this.observePerformanceEntry('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1]
      if (lastEntry) {
        this.recordMetric('LCP', lastEntry.startTime, {
          type: 'web-vital',
          unit: 'ms',
          element: lastEntry.element?.tagName
        })
      }
    })
    
    // First Input Delay (FID)
    this.observePerformanceEntry('first-input', (entries) => {
      entries.forEach(entry => {
        this.recordMetric('FID', entry.processingStart - entry.startTime, {
          type: 'web-vital',
          unit: 'ms',
          eventType: entry.name
        })
      })
    })
    
    // Cumulative Layout Shift (CLS)
    let clsValue = 0
    this.observePerformanceEntry('layout-shift', (entries) => {
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      })
      
      this.recordMetric('CLS', clsValue, {
        type: 'web-vital',
        unit: 'score'
      })
    })
  }
  
  // 监控资源加载
  observeResourceTiming() {
    this.observePerformanceEntry('resource', (entries) => {
      entries.forEach(entry => {
        const duration = entry.responseEnd - entry.startTime
        const size = entry.transferSize || 0
        
        // 记录大文件加载时间
        if (size > 100000) { // 100KB
          this.recordMetric('large-resource-load', duration, {
            type: 'resource',
            url: entry.name,
            size: size,
            resourceType: this.getResourceType(entry.name)
          })
        }
        
        // 记录慢速资源
        if (duration > 1000) { // 1秒
          this.recordMetric('slow-resource-load', duration, {
            type: 'resource',
            url: entry.name,
            size: size,
            resourceType: this.getResourceType(entry.name)
          })
        }
      })
    })
  }
  
  // 监控用户自定义时间
  observeUserTiming() {
    this.observePerformanceEntry('measure', (entries) => {
      entries.forEach(entry => {
        this.recordMetric(`user-timing-${entry.name}`, entry.duration, {
          type: 'user-timing',
          unit: 'ms'
        })
      })
    })
  }
  
  // 监控长任务
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            this.recordMetric('long-task', entry.duration, {
              type: 'performance',
              unit: 'ms',
              startTime: entry.startTime
            })
          })
        })
        
        observer.observe({ entryTypes: ['longtask'] })
        this.observers.push(observer)
      } catch (e) {
        console.warn('[Performance] Long task observation not supported')
      }
    }
  }
  
  // 监控布局偏移
  observeLayoutShift() {
    this.observePerformanceEntry('layout-shift', (entries) => {
      entries.forEach(entry => {
        if (entry.value > 0.1) { // 显著的布局偏移
          this.recordMetric('significant-layout-shift', entry.value, {
            type: 'performance',
            unit: 'score',
            hadRecentInput: entry.hadRecentInput
          })
        }
      })
    })
  }
  
  // 通用性能条目观察器
  observePerformanceEntry(entryType, callback) {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          callback(list.getEntries())
        })
        
        observer.observe({ entryTypes: [entryType] })
        this.observers.push(observer)
      } catch (e) {
        console.warn(`[Performance] ${entryType} observation not supported`)
      }
    }
  }
  
  // 设置错误处理
  setupErrorHandling() {
    // JavaScript 错误
    window.addEventListener('error', (event) => {
      this.recordMetric('javascript-error', 1, {
        type: 'error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    })
    
    // Promise 拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.recordMetric('unhandled-promise-rejection', 1, {
        type: 'error',
        reason: event.reason?.toString()
      })
    })
    
    // 资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.recordMetric('resource-error', 1, {
          type: 'error',
          resourceType: event.target.tagName,
          source: event.target.src || event.target.href
        })
      }
    }, true)
  }
  
  // 设置页面卸载处理
  setupUnloadHandler() {
    const sendBeacon = () => {
      this.recordMetric('session-duration', performance.now() - this.startTime, {
        type: 'session',
        unit: 'ms'
      })
      
      this.sendMetrics(true) // 强制发送
    }
    
    // 页面卸载时发送数据
    window.addEventListener('beforeunload', sendBeacon)
    window.addEventListener('pagehide', sendBeacon)
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        sendBeacon()
      }
    })
  }
  
  // 记录自定义指标
  recordCustomMetric(name, value, metadata = {}) {
    this.recordMetric(`custom-${name}`, value, {
      type: 'custom',
      ...metadata
    })
  }
  
  // 记录支付相关指标
  recordPaymentMetric(action, duration, metadata = {}) {
    this.recordMetric(`payment-${action}`, duration, {
      type: 'payment',
      unit: 'ms',
      ...metadata
    })
  }
  
  // 记录指标
  recordMetric(name, value, metadata = {}) {
    const metric = {
      name,
      value,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...metadata
    }
    
    this.metrics.push(metric)
    
    // 批量发送
    if (this.metrics.length >= this.options.batchSize) {
      this.sendMetrics()
    }
    
    console.log(`[Performance] ${name}: ${value}`, metadata)
  }
  
  // 发送指标数据
  async sendMetrics(force = false) {
    if (this.metrics.length === 0) return
    
    const metricsToSend = [...this.metrics]
    this.metrics = []
    
    try {
      if (force && 'sendBeacon' in navigator) {
        // 使用 sendBeacon 确保数据发送
        navigator.sendBeacon(
          this.options.reportUrl,
          JSON.stringify({ metrics: metricsToSend })
        )
      } else {
        // 普通 fetch 请求
        await fetch(this.options.reportUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ metrics: metricsToSend })
        })
      }
      
      console.log(`[Performance] Sent ${metricsToSend.length} metrics`)
    } catch (error) {
      console.error('[Performance] Failed to send metrics:', error)
      // 发送失败时重新加入队列
      this.metrics.unshift(...metricsToSend)
    }
  }
  
  // 获取资源类型
  getResourceType(url) {
    if (url.match(/\.(js)$/)) return 'script'
    if (url.match(/\.(css)$/)) return 'stylesheet'
    if (url.match(/\.(png|jpe?g|gif|svg|webp)$/)) return 'image'
    if (url.match(/\.(woff2?|ttf|eot)$/)) return 'font'
    return 'other'
  }
  
  // 判断是否应该采样
  shouldSample() {
    return Math.random() < this.options.sampleRate
  }
  
  // 销毁监控器
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.sendMetrics(true)
  }
}

// 支付页面专用性能监控
class PaymentPerformanceMonitor extends PerformanceMonitor {
  constructor(options = {}) {
    super(options)
    this.setupPaymentSpecificMonitoring()
  }
  
  setupPaymentSpecificMonitoring() {
    // 监控支付组件加载时间
    this.monitorPaymentComponentLoad()
    
    // 监控支付流程关键步骤
    this.monitorPaymentFlow()
    
    // 监控第三方SDK加载
    this.monitorThirdPartySDK()
  }
  
  monitorPaymentComponentLoad() {
    // 监控 Adyen 组件加载
    const originalAdyenLoad = window.AdyenCheckout
    if (originalAdyenLoad) {
      const startTime = performance.now()
      this.recordPaymentMetric('adyen-component-load', performance.now() - startTime)
    }
  }
  
  monitorPaymentFlow() {
    // 可以通过事件监听或方法包装来监控支付流程
    this.recordCustomMetric('payment-page-loaded', performance.now())
  }
  
  monitorThirdPartySDK() {
    // 监控第三方SDK的加载时间
    const sdks = ['AdyenCheckout', 'Airwallex', 'CheckoutWebComponents']
    
    sdks.forEach(sdk => {
      if (window[sdk]) {
        this.recordCustomMetric(`${sdk.toLowerCase()}-sdk-available`, performance.now())
      }
    })
  }
}

// 导出监控器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { PerformanceMonitor, PaymentPerformanceMonitor }
} else {
  window.PerformanceMonitor = PerformanceMonitor
  window.PaymentPerformanceMonitor = PaymentPerformanceMonitor
}

// 自动初始化（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  // 等待 DOM 加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.performanceMonitor = new PaymentPerformanceMonitor({
        sampleRate: 0.1, // 10% 采样率
        reportUrl: '/api/performance'
      })
    })
  } else {
    window.performanceMonitor = new PaymentPerformanceMonitor({
      sampleRate: 0.1,
      reportUrl: '/api/performance'
    })
  }
}
