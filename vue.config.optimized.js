/**
 * Vue.js 性能优化配置文件
 * 基于 Web Pay Hub 项目的性能分析报告
 * 
 * 主要优化点：
 * 1. JavaScript代码分割
 * 2. 图片压缩和现代格式支持
 * 3. Gzip压缩
 * 4. CSS优化
 * 5. 缓存策略
 */

const CopyWebpackPlugin = require('copy-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const ImageminPlugin = require('imagemin-webpack-plugin').default
const imageminMozjpeg = require('imagemin-mozjpeg')
const imageminPngquant = require('imagemin-pngquant')
const imageminWebp = require('imagemin-webp')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const path = require('path')
const AllPageConfig = require('./config/metaConfig.js')

const GAME = process.env.VUE_APP_GAME
const isDev = process.env.NODE_ENV === 'development'
const isProd = process.env.NODE_ENV === 'production'
const timeMark = getDateMark()

const config = {
  publicPath: isProd ? process.env.VUE_APP_CDN_URL || '/res/' : '/',
  lintOnSave: false,
  
  // 生产环境关闭source map以减小包体积
  productionSourceMap: false,
  
  configureWebpack: {
    // 开发环境启用source map
    devtool: isDev ? 'eval-cheap-module-source-map' : false,
    
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        // 使用运行时版本减小包体积
        'vue$': 'vue/dist/vue.runtime.esm.js'
      },
      // 优化模块解析
      modules: ['node_modules'],
      extensions: ['.js', '.vue', '.json']
    },
    
    plugins: [
      // 复制静态资源
      new CopyWebpackPlugin([
        { from: './robots.txt' },
        { from: './config/pwa', to: 'config' },
        { from: './config/service-worker.js' }
      ]),
      
      // 生产环境启用Gzip压缩
      ...(isProd ? [
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|svg)$/,
          threshold: 8192, // 8KB以上才压缩
          minRatio: 0.8,
          deleteOriginalAssets: false
        }),
        
        // 图片压缩和WebP转换
        new ImageminPlugin({
          test: /\.(jpe?g|png|gif|svg)$/i,
          pngquant: {
            quality: '65-90'
          },
          plugins: [
            imageminMozjpeg({
              quality: 80,
              progressive: true
            }),
            imageminPngquant({
              quality: [0.65, 0.90],
              speed: 4
            }),
            imageminWebp({
              quality: 75
            })
          ]
        })
      ] : []),
      
      // 开发环境启用包分析器（可选）
      ...(process.env.ANALYZE ? [new BundleAnalyzerPlugin()] : [])
    ],
    
    optimization: {
      // 启用Tree Shaking
      usedExports: true,
      sideEffects: false,
      
      // 代码分割策略
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          // 第三方库单独打包
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          
          // 支付相关组件单独打包（最大的优化点）
          payment: {
            name: 'chunk-payment',
            test: /[\\/](adyen|airwallex|checkout)[\\/]/,
            priority: 20,
            chunks: 'async',
            enforce: true
          },
          
          // Swiper组件单独打包
          swiper: {
            name: 'chunk-swiper',
            test: /[\\/]swiper[\\/]/,
            priority: 15,
            chunks: 'async'
          },
          
          // 国际化文件单独打包
          i18n: {
            name: 'chunk-i18n',
            test: /[\\/]langHelper[\\/]/,
            priority: 12,
            chunks: 'async'
          },
          
          // 公共组件
          common: {
            name: 'chunk-common',
            minChunks: 2,
            priority: 5,
            chunks: 'initial',
            reuseExistingChunk: true
          },
          
          // 工具函数
          utils: {
            name: 'chunk-utils',
            test: /[\\/]src[\\/]utils[\\/]/,
            priority: 8,
            chunks: 'initial'
          }
        }
      },
      
      // 运行时chunk单独提取
      runtimeChunk: {
        name: 'runtime'
      }
    },
    
    // 外部依赖CDN化（可选）
    externals: isProd ? {
      // 'vue': 'Vue',
      // 'vue-router': 'VueRouter',
      // 'vuex': 'Vuex'
    } : {}
  },
  
  pages: {},
  
  chainWebpack: config => {
    // 删除预加载和预获取（根据需要调整）
    config.plugins.delete('preload')
    config.plugins.delete('prefetch')
    
    // 图片处理优化
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 4096, // 4KB以下转base64
        name: `static/${timeMark}/img/[name].[hash:8].[ext]`,
        fallback: {
          loader: 'file-loader',
          options: {
            name: `static/${timeMark}/img/[name].[hash:8].[ext]`
          }
        }
      })
    
    // 字体文件处理
    config.module
      .rule('fonts')
      .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/i)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 4096,
        name: `static/${timeMark}/fonts/[name].[hash:8].[ext]`
      })
    
    // CSS优化
    if (isProd) {
      config.plugin('extract-css')
        .tap(args => {
          args[0].filename = `static/${timeMark}/css/[name].[contenthash:8].css`
          args[0].chunkFilename = `static/${timeMark}/css/[name].[contenthash:8].css`
          return args
        })
    }
    
    // HTML模板优化
    config.plugin('html')
      .tap(args => {
        args[0].minify = isProd ? {
          removeComments: true,
          collapseWhitespace: true,
          removeAttributeQuotes: true,
          collapseBooleanAttributes: true,
          removeScriptTypeAttributes: true
        } : false
        return args
      })
  },
  
  // CSS相关配置
  css: {
    // 生产环境提取CSS
    extract: isProd ? {
      filename: `static/${timeMark}/css/[name].[contenthash:8].css`,
      chunkFilename: `static/${timeMark}/css/[name].[contenthash:8].css`
    } : false,
    
    // CSS source map
    sourceMap: isDev,
    
    loaderOptions: {
      // PostCSS配置
      postcss: {
        plugins: [
          require('autoprefixer'),
          require('postcss-pxtorem')({
            rootValue: 37.5,
            propList: ['*'],
            exclude: /Adyen|airwallex/i,
            mediaQuery: false
          })
        ]
      },
      
      // Sass配置
      sass: {
        additionalData: `@import "@/utils/utils.scss";`
      }
    }
  },
  
  // 开发服务器配置
  devServer: {
    overlay: {
      warnings: false,
      errors: true
    },
    // 启用gzip压缩
    compress: true,
    // 性能优化
    hot: true,
    // 减少编译输出
    stats: 'minimal'
  }
}

// 更新页面配置
updatePageConfig(AllPageConfig)

// 环境特定配置
try {
  const scriptParams = process.env.npm_lifecycle_event
  const [action, env] = scriptParams.split(':') || []

  if (action === 'build') {
    config.outputDir = 'dist_' + env
    
    if (env === 'online') {
      Object.assign(config, {
        publicPath: process.env.onlinePublicGlobalPath,
        outputDir: 'dist_online',
        assetsDir: `static/${timeMark}`,
        indexPath: 'index.html',
        productionSourceMap: false,
        crossorigin: 'anonymous'
      })
    }
  }
} catch (e) {
  console.log('配置解析错误:', e)
}

// 页面配置函数（保持原有逻辑）
function risePage(curGame, PageInfo) {
  const isOnline = process.env.VUE_APP_PROD_ENV === 'ONLINE'
  const isBuild = process.env.NODE_ENV === 'production'
  
  let resBathPath = '/'
  if (isBuild && PageInfo.linkMainDomain) resBathPath = `/${PageInfo.linkPathName}/`
  if (isOnline) {
    resBathPath = curGame.includes('cn') ? process.env.onlinePublicCnPath : process.env.onlinePublicGlobalPath
  }

  // 根据游戏类型返回不同配置
  if (curGame === 'pc') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/pc.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      resBathPath,
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }

  if (curGame === 'sdk') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/sdk.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      resBathPath,
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }

  return {
    template: 'public/index.html',
    entry: '/src/main.js',
    filename: isDev ? 'index.html' : `index_${curGame}.html`,
    game: curGame,
    title: PageInfo.title,
    description: PageInfo.description,
    icon: PageInfo.icon,
    themeColor: PageInfo.themeColor || 'white',
    linkMainDomain: PageInfo.linkMainDomain || false,
    linkPathName: PageInfo.linkPathName,
    manifestPath: (isOnline && PageInfo.linkMainDomain ? `https://store.funplus.com/subservice/${curGame}/` : 'config/') + `${curGame}.manifest.webmanifest`,
    resBathPath
  }
}

function updatePageConfig(allConfig) {
  if (isDev) {
    const devGame = GAME
    if (!devGame) {
      console.log('pageConfig[\'devGame\'] 不能为空！')
      process.exit(0)
    }
    config.pages.index = risePage(devGame, allConfig[devGame])
  } else {
    config.publicPath = '/res/'
    for (const [curGame, curConfig] of Object.entries(allConfig)) {
      config.pages[curGame] = risePage(curGame, curConfig)
    }
  }
}

function getDateMark() {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1

  let targetMonth
  if (month <= 3) {
    targetMonth = 1
  } else if (month <= 6) {
    targetMonth = 4
  } else if (month <= 9) {
    targetMonth = 7
  } else {
    targetMonth = 10
  }

  const targetDate = new Date(year, targetMonth - 1, 1)
  return Math.floor(targetDate.getTime() / 1000)
}

module.exports = config
